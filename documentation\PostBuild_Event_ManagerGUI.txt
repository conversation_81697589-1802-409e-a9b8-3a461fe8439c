SET BUILD_PATH=$(SolutionDir)\Dist
SET SMM_PATH=$(SolutionDir)\Dist\SMM
SET MODS_PATH=$(SolutionDir)\Dist\mods

SET LIB_DIR=$(SolutionDir)\libs
SET PROJECT_DOORSTOP=$(SolutionDir)\Doorstop
SET PROJECT_MANAGER=$(SolutionDir)\Manager
SET PROJECT_MODAPI=$(SolutionDir)\ModAPI

SET PROJECT_PLUGIN_CONSOLE=$(SolutionDir)\PluginConsole
SET PROJECT_PLUGIN_DEBUG_GUI=$(SolutionDir)\PluginDebugGUI
SET PROJECT_PLUGIN_HARMONY=$(SolutionDir)\PluginHarmony
SET PROJECT_PLUGIN_INITIALIZER=$(SolutionDir)\PluginInitializer


SET SUB_DIR=\bin\debug\

:: initialize the \Dist\ directory
rd /s /q "%BUILD_PATH%"
mkdir "%BUILD_PATH%"

:: initialize the \Dist\SMM\ directory
rd /s /q "%SMM_PATH%"
mkdir "%SMM_PATH%"



:: Copy the manager exe to the Dist-directory 
copy "%PROJECT_MANAGER%"\"%SUB_DIR%"\Manager.exe "%SMM_PATH%"\

:: Copy the Mod-API.dll to the Dist-directory 
copy "%PROJECT_MODAPI%"\"%SUB_DIR%"\ModAPI.dll "%SMM_PATH%"\

:: Copy the Doorstop-Hoock and its config the Dist-directory 
copy "%PROJECT_DOORSTOP%"\"%SUB_DIR%"\Doorstop.dll "%SMM_PATH%"\
copy "%LIB_DIR%"\doorstop_config.ini "%BUILD_PATH%"\
copy "%LIB_DIR%"\winhttp.dll "%BUILD_PATH%"\


:: initialize the \Dist\mods - directory
if exist "%MODS_PATH%" rd /s /q "%MODS_PATH%"
mkdir "%MODS_PATH%"



:: Copy the Mod-API.dll to the Dist-directory 
copy "%PROJECT_PLUGIN_CONSOLE%"\"%SUB_DIR%"\PluginConsole.dll "%MODS_PATH%"\
copy "%PROJECT_PLUGIN_DEBUG_GUI%"\"%SUB_DIR%"\PluginDebugGUI.dll "%MODS_PATH%"\
copy "%PROJECT_PLUGIN_HARMONY%"\"%SUB_DIR%"\PluginHarmony.dll "%MODS_PATH%"\
copy "%PROJECT_PLUGIN_INITIALIZER%"\"%SUB_DIR%"\PluginInitializer.dll "%MODS_PATH%"\
copy "%LIB_DIR%\"0Harmony.dll* "%MODS_PATH%"\
