<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{5297E8BC-3D78-4904-9A81-001A86E02F15}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>Manager</RootNamespace>
    <AssemblyName>Manager</AssemblyName>
    <TargetFrameworkVersion>v3.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject>Manager.Program</StartupObject>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>icon.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Assembly-CSharp">
      <HintPath>..\..\..\..\Epic Games\Sheltered\ShelteredWindows64_EOS_Data\Managed\Assembly-CSharp.dll</HintPath>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Web.Extensions" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ManagerGUI.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ManagerGUI.Designer.cs">
      <DependentUpon>ManagerGUI.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <EmbeddedResource Include="ManagerGUI.resx">
      <DependentUpon>ManagerGUI.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="logo.jpg" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\ModAPI\ModAPI.csproj">
      <Project>{3c99d6bf-a4a9-45dc-bef1-5717ece2a687}</Project>
      <Name>ModAPI</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="icon.ico" />
    <None Include="logo.png" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Lib.Harmony">
      <Version>2.4.1</Version>
    </PackageReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PostBuildEvent>SET BUILD_PATH=$(SolutionDir)\Dist
SET SMM_PATH=$(SolutionDir)\Dist\SMM
SET MODS_PATH=$(SolutionDir)\Dist\mods

SET LIB_DIR=$(SolutionDir)\libs
SET PROJECT_DOORSTOP=$(SolutionDir)\Doorstop
SET PROJECT_MANAGER=$(SolutionDir)\Manager
SET PROJECT_MODAPI=$(SolutionDir)\ModAPI

SET PROJECT_PLUGIN_CONSOLE=$(SolutionDir)\PluginConsole
SET PROJECT_PLUGIN_DEBUG_GUI=$(SolutionDir)\PluginDebugGUI
SET PROJECT_PLUGIN_HARMONY=$(SolutionDir)\PluginHarmony
SET PROJECT_PLUGIN_INITIALIZER=$(SolutionDir)\PluginInitializer


SET SUB_DIR=\bin\debug\

:: initialize the \Dist\ directory
rd /s /q "%25BUILD_PATH%25"
mkdir "%25BUILD_PATH%25"

:: initialize the \Dist\SMM\ directory
rd /s /q "%25SMM_PATH%25"
mkdir "%25SMM_PATH%25"



:: Copy the manager exe to the Dist-directory 
copy "%25PROJECT_MANAGER%25"\"%25SUB_DIR%25"\Manager.exe "%25SMM_PATH%25"\

:: Copy the Mod-API.dll to the Dist-directory 
copy "%25PROJECT_MODAPI%25"\"%25SUB_DIR%25"\ModAPI.dll "%25SMM_PATH%25"\

:: Copy the Doorstop-Hoock and its config the Dist-directory 
copy "%25PROJECT_DOORSTOP%25"\"%25SUB_DIR%25"\Doorstop.dll "%25SMM_PATH%25"\
copy "%25PROJECT_DOORSTOP%25"\"%25SUB_DIR%25"\Doorstop.pdb "%25SMM_PATH%25"\
copy "%25LIB_DIR%25"\doorstop_config.ini "%25BUILD_PATH%25"\
copy "%25LIB_DIR%25"\winhttp.dll "%25BUILD_PATH%25"\


:: initialize the \Dist\mods - directory
if exist "%25MODS_PATH%25" rd /s /q "%25MODS_PATH%25"
mkdir "%25MODS_PATH%25"



:: Copy the Mod-API.dll to the Dist-directory 
copy "%25PROJECT_PLUGIN_CONSOLE%25"\"%25SUB_DIR%25\"\PluginConsole.dll "%25MODS_PATH%25"\
copy "%25PROJECT_PLUGIN_CONSOLE%25"\"%25SUB_DIR%25\"\PluginConsole.pdb "%25MODS_PATH%25"\
copy "%25PROJECT_PLUGIN_DEBUG_GUI%25"\"%25SUB_DIR%25"\PluginDebugGUI.dll "%25MODS_PATH%25"\
copy "%25PROJECT_PLUGIN_HARMONY%25"\"%25SUB_DIR%25"\PluginHarmony.dll "%25MODS_PATH%25"\
copy "%25PROJECT_PLUGIN_INITIALIZER%25"\"%25SUB_DIR%25"\PluginInitializer.dll "%25MODS_PATH%25"\
copy "%25LIB_DIR%25"\0Harmony.dll* "%25MODS_PATH%25"\
</PostBuildEvent>
  </PropertyGroup>
</Project>
